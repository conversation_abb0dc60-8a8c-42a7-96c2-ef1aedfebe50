/**
 * Custom Admin Fixes CSS
 * Bu dosya, WordPress admin panelindeki Tutor LMS menüsünde bulunan
 * "Pro'ya yükselt" menü öğesini gizlemek için kullanılır.
 * Ayrıca kullanıcı profil sayfasındaki "Araç çubuğu" yazısını ve seçeneğini gizler.
 *
 * @package DmrLMS
 * @since 1.0.1
 */

/* "Pro'ya yükselt" menü öğesini gizle */
#adminmenu li#toplevel_page_tutor a[href="https://www.themeum.com/product/tutor-lms/"],
#adminmenu li#toplevel_page_tutor a[href*="tutor-pro-page"],
#adminmenu li#toplevel_page_tutor ul.wp-submenu li a[href*="tutor-pro-page"],
#adminmenu li#toplevel_page_tutor ul.wp-submenu li:last-child {
    display: none !important;
}

/* Tutor LMS menüsündeki diğer öğelerin stillerini düzelt */
#adminmenu li#toplevel_page_tutor ul.wp-submenu {
    padding-bottom: 5px !important;
}

/* Kullanıcı profil sayfasındaki "Araç çubuğu" yazısını ve seçeneğini gizle */
/* NOT: Bu CSS kuralları artık PHP tarafında dinamik olarak ekleniyor */
/* Sadece admin olmayan kullanıcılar için araç çubuğu seçeneği gizlenir */
/* Admin kullanıcıları araç çubuğu seçeneğini görebilir ve değiştirebilir */

/* Dashboard sidebar'ındaki eğitmen menü öğelerini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-my-courses,
.tutor-dashboard-menu-item.tutor-dashboard-menu-announcements,
.tutor-dashboard-menu-item.tutor-dashboard-menu-withdraw,
.tutor-dashboard-menu-item.tutor-dashboard-menu-quiz-attempts,
.tutor-dashboard-menu-divider-header {
    display: none !important;
    visibility: hidden !important;
}

/* CSS seçicileri ile de gizle (ek güvenlik) */
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-my-courses,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-announcements,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-withdraw,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-quiz-attempts,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-divider-header {
    display: none !important;
    visibility: hidden !important;
}

/* Soru Oluştur Modal Stilleri */
#dmr-create-question-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: none; /* Başlangıçta gizli */
    align-items: center;
    justify-content: center;
}

#dmr-create-question-modal.show {
    display: flex !important;
}

#dmr-create-question-modal .tutor-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

#dmr-create-question-modal .tutor-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#dmr-create-question-modal .tutor-modal-title h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

#dmr-create-question-modal .tutor-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

#dmr-create-question-modal .tutor-modal-close:hover {
    background-color: #f3f4f6;
    color: #374151;
}

#dmr-create-question-modal .tutor-modal-body {
    padding: 24px;
}

#dmr-create-question-modal .tutor-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

#dmr-create-question-modal .tutor-form-control,
#dmr-create-question-modal .tutor-form-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

#dmr-create-question-modal .tutor-form-control:focus,
#dmr-create-question-modal .tutor-form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

#dmr-create-question-modal textarea.tutor-form-control {
    resize: vertical;
    min-height: 120px;
}

#dmr-create-question-modal .tutor-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
}

#dmr-create-question-modal .tutor-btn-primary {
    background-color: #3b82f6;
    color: white;
}

#dmr-create-question-modal .tutor-btn-primary:hover {
    background-color: #2563eb;
}

#dmr-create-question-modal .tutor-btn-ghost {
    background-color: transparent;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

#dmr-create-question-modal .tutor-btn-ghost:hover {
    background-color: #f9fafb;
    color: #374151;
}

/* Modal açıkken body scroll'unu engelle */
body.modal-open {
    overflow: hidden;
}

/* Responsive modal */
@media (max-width: 768px) {
    #dmr-create-question-modal .tutor-modal-content {
        width: 95%;
        margin: 20px;
    }

    #dmr-create-question-modal .tutor-modal-header,
    #dmr-create-question-modal .tutor-modal-body {
        padding: 16px;
    }
}
