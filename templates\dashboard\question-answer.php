<?php
/**
 * Dashboard Question Answer Page
 *
 * @package Tu<PERSON>\Templates
 * @subpackage Dashboard
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @version 1.6.4
 */

use TUTOR\Input;
use TUTOR\Instructor;
use TUTOR\Q_And_A;

$question_id = Input::get( 'question_id', null, Input::TYPE_INT );
if ( $question_id ) {
	$question = tutor_utils()->get_qa_question( $question_id );
	$user_id  = get_current_user_id();

	if ( $question && ! Q_And_A::has_qna_access( $user_id, $question->comment_post_ID ) ) {
		tutor_utils()->tutor_empty_state( tutor_utils()->error_message() );
		return;
	}

	tutor_load_template_from_custom_path(
		tutor()->path . '/views/qna/qna-single.php',
		array(
			'question_id' => $question_id,
			'context'     => 'frontend-dashboard-qna-single',
		)
	);
	return;
}

if ( in_array( Input::get( 'view_as' ), array( 'student', 'instructor' ) ) ) {
	update_user_meta( get_current_user_id(), 'tutor_qa_view_as', Input::get( 'view_as' ) );
}

$is_instructor     = tutor_utils()->is_instructor( null, true );
$view_option       = get_user_meta( get_current_user_id(), 'tutor_qa_view_as', true );
// Her zaman ogrenci gorunumu goster - eğitmen kısmını gizle
$view_as           = 'student';
$as_instructor_url = add_query_arg( array( 'view_as' => 'instructor' ), tutor()->current_url );
$as_student_url    = add_query_arg( array( 'view_as' => 'student' ), tutor()->current_url );
$qna_tabs          = \Tutor\Q_And_A::tabs_key_value( 'student' == $view_as ? get_current_user_id() : null );
$active_tab        = Input::get( 'tab', 'all' );
?>

<div class="tutor-frontend-dashboard-qna-header tutor-mb-32">
	<div class="tutor-row tutor-mb-24">
		<div class="tutor-col">
			<div class="tutor-fs-5 tutor-fw-medium tutor-color-black">
				<?php esc_html_e( 'Question & Answer', 'tutor' ); ?>
			</div>
		</div>

		<div class="tutor-col-auto">
			<button type="button" class="tutor-btn tutor-btn-primary" id="dmr-create-question-btn" onclick="dmrOpenModal()">
				<i class="tutor-icon-plus tutor-mr-8"></i>
				<?php esc_html_e( 'Soru Oluştur', 'dmr-lms' ); ?>
			</button>
		</div>

		<?php
		// Eğitmen/Öğrenci toggle kısmı gizlendi - sadece öğrenci görünümü gösterilecek
		// if ( $is_instructor ) : ?>
			<!-- Toggle kısmı kaldırıldı -->
		<?php // endif; ?>
	</div>

		<div class="tutor-row">
			<div class="tutor-col-lg-5">
				<div class="tutor-qna-filter tutor-d-flex tutor-align-center">
					<span class="tutor-fs-7 tutor-color-secondary tutor-mr-20"><?php esc_html_e( 'Sort By', 'tutor' ); ?>:</span>
					<div class="tutor-flex-grow-1">
						<select class="tutor-form-select tutor-select-redirector">
							<?php
							foreach ( $qna_tabs as $tab ) {
								$markup = '<option value="' . $tab['url'] . '" ' . ( $active_tab == $tab['key'] ? 'selected="selected"' : '' ) . '>
                                        ' . $tab['title'] . '(' . $tab['value'] . ')' . '
                                    </option>';
								echo wp_kses(
									$markup,
									array(
										'option' => array(
											'value'    => true,
											'selected' => true,
										),
									)
								);
							}
							?>
						</select>
					</div>
				</div>
			</div>
		</div>
</div>

<?php
$per_page     = tutor_utils()->get_option( 'pagination_per_page', 10 );
$current_page = max( 1, tutor_utils()->avalue_dot( 'current_page', $_GET ) );
$offset       = ( $current_page - 1 ) * $per_page;

$q_status    = Input::get( 'tab' );
// Her zaman sadece mevcut kullanıcının sorularını goster (ogrenci gorunumu)
$asker_id    = get_current_user_id();
$total_items = tutor_utils()->get_qa_questions( $offset, $per_page, '', null, null, $asker_id, $q_status, true );
$questions   = tutor_utils()->get_qa_questions( $offset, $per_page, '', null, null, $asker_id, $q_status );

tutor_load_template_from_custom_path(
	tutor()->path . '/views/qna/qna-table.php',
	array(
		'qna_list'       => $questions,
		'context'        => 'frontend-dashboard-qna-table-' . $view_as,
		'view_as'        => $view_as,
		'qna_pagination' => array(
			'base'        => '?current_page=%#%',
			'total_items' => $total_items,
			'per_page'    => $per_page,
			'paged'       => $current_page,
		),
	)
);
?>

<!-- Soru Oluştur Modal -->
<div id="dmr-create-question-modal" class="tutor-modal-wrap">
	<div class="tutor-modal-content">
		<div class="tutor-modal-header">
			<div class="tutor-modal-title">
				<h4><?php esc_html_e( 'Yeni Soru Oluştur', 'dmr-lms' ); ?></h4>
			</div>
			<button type="button" class="tutor-modal-close" id="dmr-close-question-modal" onclick="dmrCloseModal()">
				<i class="tutor-icon-times"></i>
			</button>
		</div>
		<div class="tutor-modal-body">
			<form id="dmr-create-question-form" method="post">
				<?php wp_nonce_field( tutor()->nonce_action, tutor()->nonce ); ?>
				<input type="hidden" name="action" value="tutor_place_question" />

				<div class="tutor-form-group tutor-mb-24">
					<label class="tutor-form-label">
						<?php esc_html_e( 'Kurs Seçin', 'dmr-lms' ); ?>
					</label>
					<select name="course_id" class="tutor-form-select" required>
						<option value=""><?php esc_html_e( 'Bir kurs seçin...', 'dmr-lms' ); ?></option>
						<?php
						// Kullanıcının kayıtlı olduğu kursları getir
						$user_id = get_current_user_id();
						$enrolled_courses = tutor_utils()->get_enrolled_courses_by_user( $user_id, array( 'private', 'publish' ) );

						if ( $enrolled_courses && $enrolled_courses->have_posts() ) {
							while ( $enrolled_courses->have_posts() ) {
								$enrolled_courses->the_post();
								echo '<option value="' . esc_attr( get_the_ID() ) . '">' . esc_html( get_the_title() ) . '</option>';
							}
							wp_reset_postdata();
						}
						?>
					</select>
				</div>

				<div class="tutor-form-group tutor-mb-24">
					<label class="tutor-form-label">
						<?php esc_html_e( 'Soru Başlığı', 'dmr-lms' ); ?>
					</label>
					<input type="text" name="question_title" class="tutor-form-control" placeholder="<?php esc_attr_e( 'Sorunuzun başlığını yazın...', 'dmr-lms' ); ?>" required />
				</div>

				<div class="tutor-form-group tutor-mb-24">
					<label class="tutor-form-label">
						<?php esc_html_e( 'Soru İçeriği', 'dmr-lms' ); ?>
					</label>
					<textarea name="question_content" class="tutor-form-control" rows="6" placeholder="<?php esc_attr_e( 'Sorunuzun detaylarını yazın...', 'dmr-lms' ); ?>" required></textarea>
				</div>

				<div class="tutor-form-group">
					<div class="tutor-d-flex tutor-justify-end">
						<button type="button" class="tutor-btn tutor-btn-ghost tutor-mr-16" id="dmr-cancel-question" onclick="dmrCloseModal()">
							<?php esc_html_e( 'İptal', 'dmr-lms' ); ?>
						</button>
						<button type="submit" class="tutor-btn tutor-btn-primary">
							<?php esc_html_e( 'Soru Gönder', 'dmr-lms' ); ?>
						</button>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>

<script>
function dmrOpenModal() {
    console.log('dmrOpenModal çalıştı');
    const modal = document.getElementById('dmr-create-question-modal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.classList.add('modal-open');
        console.log('Modal açıldı');
    } else {
        console.error('Modal bulunamadı');
    }
}

function dmrCloseModal() {
    console.log('dmrCloseModal çalıştı');
    const modal = document.getElementById('dmr-create-question-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.classList.remove('modal-open');
        console.log('Modal kapatıldı');
    }
}

// Modal kapatma butonları için event listener
document.addEventListener('DOMContentLoaded', function() {
    const closeBtn = document.getElementById('dmr-close-question-modal');
    const cancelBtn = document.getElementById('dmr-cancel-question');

    if (closeBtn) {
        closeBtn.addEventListener('click', dmrCloseModal);
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', dmrCloseModal);
    }

    // Modal dışına tıklandığında kapat
    const modal = document.getElementById('dmr-create-question-modal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                dmrCloseModal();
            }
        });
    }
});
</script>
